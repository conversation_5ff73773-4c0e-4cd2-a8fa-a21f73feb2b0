# Generated by Django 4.2.13 on 2024-11-07 05:24

from django.db import migrations, models
import django.db.models.deletion
import utilmeta.core.orm.backends.django.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="BaseContent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("public", models.BooleanField(default=False)),
                ("type", models.CharField(default="article", max_length=20)),
            ],
            options={
                "db_table": "content",
                "ordering": ["-created_at", "-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="Follow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("follow_time", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "follow",
            },
        ),
        migrations.CreateModel(
            name="Article",
            fields=[
                (
                    "basecontent_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="app.basecontent",
                    ),
                ),
                ("title", models.CharField(max_length=40)),
                ("description", models.TextField(default="")),
                ("slug", models.SlugField(unique=True)),
                ("views", models.PositiveIntegerField(default=0)),
            ],
            options={
                "db_table": "article",
            },
            bases=("app.basecontent",),
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(max_length=20, unique=True)),
                (
                    "password",
                    utilmeta.core.orm.backends.django.models.PasswordField(
                        max_length=20, min_length=6, regex=None, salt_length=32
                    ),
                ),
                ("jwt_token", models.TextField(default=None, null=True)),
                (
                    "avatar",
                    models.FileField(default=None, null=True, upload_to="image/avatar"),
                ),
                ("admin", models.BooleanField(default=False)),
                ("signup_time", models.DateTimeField(auto_now_add=True)),
                ("last_login_time", models.DateTimeField(auto_now=True)),
                (
                    "last_login_ip",
                    models.GenericIPAddressField(default=None, null=True),
                ),
                ("last_activity", models.DateTimeField(auto_now=True)),
                (
                    "followers",
                    models.ManyToManyField(
                        related_name="followings", through="app.Follow", to="app.user"
                    ),
                ),
            ],
            options={
                "db_table": "user",
            },
        ),
        migrations.CreateModel(
            name="Session",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_key", models.CharField(max_length=60, unique=True)),
                ("encoded_data", models.TextField(null=True)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("last_activity", models.DateTimeField(default=None, null=True)),
                ("expiry_time", models.DateTimeField(default=None, null=True)),
                ("deleted_time", models.DateTimeField(default=None, null=True)),
                ("ip", models.GenericIPAddressField(default=None, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="app.user",
                    ),
                ),
            ],
            options={
                "db_table": "session",
            },
        ),
        migrations.AddField(
            model_name="follow",
            name="target",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_followers",
                to="app.user",
            ),
        ),
        migrations.AddField(
            model_name="follow",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="user_followings",
                to="app.user",
            ),
        ),
        migrations.AddField(
            model_name="basecontent",
            name="author",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="contents",
                to="app.user",
            ),
        ),
        migrations.AddField(
            model_name="basecontent",
            name="liked_bys",
            field=models.ManyToManyField(
                db_table="liked", related_name="likes", to="app.user"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="follow",
            unique_together={("user", "target")},
        ),
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "basecontent_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="app.basecontent",
                    ),
                ),
                (
                    "on_content",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="app.basecontent",
                    ),
                ),
            ],
            options={
                "db_table": "comment",
            },
            bases=("app.basecontent",),
        ),
    ]
