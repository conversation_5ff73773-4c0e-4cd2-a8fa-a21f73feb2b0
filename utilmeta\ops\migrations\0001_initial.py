# Generated by Django 4.2.1 on 2024-03-01 06:01

from django.db import migrations, models
import django.db.models.deletion
import utilmeta.utils.functional.time
import utype.utils.encode


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AlertLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("relieved_time", models.DateTimeField(default=None, null=True)),
                (
                    "trigger_times",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "trigger_values",
                    models.J<PERSON><PERSON>ield(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                (
                    "latest_time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("count", models.PositiveBigIntegerField(default=1)),
                (
                    "data",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_alert_log",
            },
        ),
        migrations.CreateModel(
            name="Resource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service", models.CharField(max_length=100, null=True)),
                (
                    "node_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                ("type", models.CharField(max_length=40)),
                ("ident", models.CharField(max_length=200)),
                ("route", models.CharField(max_length=300)),
                ("remote_id", models.CharField(default=None, max_length=40, null=True)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                (
                    "data",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("deleted", models.BooleanField(default=False)),
                ("deprecated", models.BooleanField(default=False)),
                (
                    "server",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resources",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_resource",
            },
        ),
        migrations.CreateModel(
            name="Supervisor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service", models.CharField(max_length=100)),
                ("node_id", models.CharField(default=None, max_length=40, null=True)),
                ("ident", models.CharField(default=None, max_length=20, null=True)),
                (
                    "backup_urls",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("base_url", models.URLField()),
                ("local", models.BooleanField(default=False)),
                ("public_key", models.TextField(default=None, null=True)),
                ("init_key", models.CharField(default=None, max_length=200, null=True)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("ops_api", models.URLField()),
                ("url", models.URLField(default=None, null=True)),
                (
                    "operation_timeout",
                    models.DecimalField(
                        decimal_places=3, default=None, max_digits=8, null=True
                    ),
                ),
                (
                    "heartbeat_interval",
                    models.PositiveIntegerField(default=None, null=True),
                ),
                ("latency", models.PositiveIntegerField(default=None, null=True)),
                (
                    "settings",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("connected", models.BooleanField(default=False)),
                ("disabled", models.BooleanField(default=False)),
                (
                    "alert_settings",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "task_settings",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "aggregate_settings",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "data",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "resources_etag",
                    models.CharField(default=None, max_length=200, null=True),
                ),
            ],
            options={
                "db_table": "utilmeta_supervisor",
            },
        ),
        migrations.CreateModel(
            name="Worker",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cpu_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "memory_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                ("used_memory", models.PositiveBigIntegerField(default=0)),
                (
                    "disk_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "file_descriptors",
                    models.PositiveIntegerField(default=None, null=True),
                ),
                ("open_files", models.PositiveBigIntegerField(default=None, null=True)),
                ("active_net_connections", models.PositiveIntegerField(default=0)),
                ("total_net_connections", models.PositiveIntegerField(default=0)),
                (
                    "net_connections_info",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("in_traffic", models.PositiveBigIntegerField(default=0, null=True)),
                ("out_traffic", models.PositiveBigIntegerField(default=0, null=True)),
                ("outbound_requests", models.PositiveIntegerField(default=0)),
                (
                    "outbound_rps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("outbound_timeouts", models.PositiveBigIntegerField(default=0)),
                ("outbound_errors", models.PositiveBigIntegerField(default=0)),
                (
                    "outbound_avg_time",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("queries_num", models.PositiveBigIntegerField(default=0)),
                (
                    "query_avg_time",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "qps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("requests", models.PositiveBigIntegerField(default=0)),
                (
                    "rps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("errors", models.PositiveBigIntegerField(default=0)),
                (
                    "avg_time",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("pid", models.PositiveIntegerField()),
                (
                    "memory_info",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("threads", models.PositiveIntegerField(default=0)),
                (
                    "start_time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("connected", models.BooleanField(default=True)),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("status", models.CharField(default=None, max_length=100, null=True)),
                ("user", models.CharField(default=None, max_length=100, null=True)),
                ("retire_time", models.DateTimeField(default=None, null=True)),
                (
                    "reload_params",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "data",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "instance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="instance_workers",
                        to="ops.resource",
                    ),
                ),
                (
                    "master",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="workers",
                        to="ops.worker",
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="server_workers",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_worker",
                "unique_together": {("server", "pid")},
            },
        ),
        migrations.CreateModel(
            name="WorkerMonitor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cpu_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "memory_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                ("used_memory", models.PositiveBigIntegerField(default=0)),
                (
                    "disk_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "file_descriptors",
                    models.PositiveIntegerField(default=None, null=True),
                ),
                ("open_files", models.PositiveBigIntegerField(default=None, null=True)),
                ("active_net_connections", models.PositiveIntegerField(default=0)),
                ("total_net_connections", models.PositiveIntegerField(default=0)),
                (
                    "net_connections_info",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("in_traffic", models.PositiveBigIntegerField(default=0, null=True)),
                ("out_traffic", models.PositiveBigIntegerField(default=0, null=True)),
                ("outbound_requests", models.PositiveIntegerField(default=0)),
                (
                    "outbound_rps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("outbound_timeouts", models.PositiveBigIntegerField(default=0)),
                ("outbound_errors", models.PositiveBigIntegerField(default=0)),
                (
                    "outbound_avg_time",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("queries_num", models.PositiveBigIntegerField(default=0)),
                (
                    "query_avg_time",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "qps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("requests", models.PositiveBigIntegerField(default=0)),
                (
                    "rps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("errors", models.PositiveBigIntegerField(default=0)),
                (
                    "avg_time",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("interval", models.PositiveIntegerField(default=None, null=True)),
                (
                    "memory_info",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("threads", models.PositiveIntegerField(default=0)),
                (
                    "metrics",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "worker",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="worker_metrics",
                        to="ops.worker",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_worker_monitor",
                "ordering": ("time",),
            },
        ),
        migrations.CreateModel(
            name="VersionLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service", models.CharField(max_length=100)),
                (
                    "node_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                ("time", models.DateTimeField(auto_now_add=True)),
                ("down_time", models.PositiveBigIntegerField(default=None, null=True)),
                ("success", models.BooleanField(default=None, null=True)),
                (
                    "restart_data",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("version", models.CharField(max_length=100)),
                (
                    "remote_id",
                    models.CharField(default=None, max_length=100, null=True),
                ),
                (
                    "instance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="restart_records",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_version_log",
            },
        ),
        migrations.CreateModel(
            name="ServiceLog",
            fields=[
                ("scheme", models.CharField(default=None, max_length=20, null=True)),
                ("method", models.CharField(default=None, max_length=20, null=True)),
                (
                    "request_type",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                (
                    "response_type",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                (
                    "request_headers",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "response_headers",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "user_agent",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                ("status", models.PositiveSmallIntegerField(default=200, null=True)),
                ("length", models.PositiveBigIntegerField(default=0, null=True)),
                (
                    "query",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "data",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "result",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                ("full_url", models.URLField(default=None, null=True)),
                ("path", models.URLField(default=None, null=True)),
                ("in_traffic", models.PositiveBigIntegerField(default=0)),
                ("out_traffic", models.PositiveBigIntegerField(default=0)),
                ("public", models.BooleanField(default=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("service", models.CharField(max_length=100)),
                (
                    "node_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                ("level", models.CharField(max_length=30)),
                ("time", models.DateTimeField()),
                ("duration", models.PositiveBigIntegerField(default=None, null=True)),
                ("thread_id", models.PositiveBigIntegerField(default=None, null=True)),
                (
                    "user_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                ("ip", models.GenericIPAddressField()),
                (
                    "trace",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "messages",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("volatile", models.BooleanField(default=True)),
                (
                    "alert",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="service_logs",
                        to="ops.alertlog",
                    ),
                ),
                (
                    "endpoint",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="endpoint_logs",
                        to="ops.resource",
                    ),
                ),
                (
                    "instance",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="instance_logs",
                        to="ops.resource",
                    ),
                ),
                (
                    "version",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="service_logs",
                        to="ops.versionlog",
                    ),
                ),
                (
                    "worker",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="logs",
                        to="ops.worker",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_service_log",
            },
        ),
        migrations.CreateModel(
            name="ServerMonitor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cpu_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "memory_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                ("used_memory", models.PositiveBigIntegerField(default=0)),
                (
                    "disk_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "file_descriptors",
                    models.PositiveIntegerField(default=None, null=True),
                ),
                ("open_files", models.PositiveBigIntegerField(default=None, null=True)),
                ("active_net_connections", models.PositiveIntegerField(default=0)),
                ("total_net_connections", models.PositiveIntegerField(default=0)),
                (
                    "net_connections_info",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("layer", models.PositiveSmallIntegerField(default=0)),
                ("interval", models.PositiveIntegerField(default=None, null=True)),
                (
                    "load_avg_1",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=8, null=True
                    ),
                ),
                (
                    "load_avg_5",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=8, null=True
                    ),
                ),
                (
                    "load_avg_15",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=8, null=True
                    ),
                ),
                (
                    "metrics",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "server",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="server_metrics",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_server_monitor",
                "ordering": ("time",),
            },
        ),
        migrations.CreateModel(
            name="RequestLog",
            fields=[
                ("scheme", models.CharField(default=None, max_length=20, null=True)),
                ("method", models.CharField(default=None, max_length=20, null=True)),
                (
                    "request_type",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                (
                    "response_type",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                (
                    "request_headers",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "response_headers",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "user_agent",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                ("status", models.PositiveSmallIntegerField(default=200, null=True)),
                ("length", models.PositiveBigIntegerField(default=0, null=True)),
                (
                    "query",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "data",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                (
                    "result",
                    models.JSONField(
                        default=None, encoder=utype.utils.encode.JSONEncoder, null=True
                    ),
                ),
                ("full_url", models.URLField(default=None, null=True)),
                ("path", models.URLField(default=None, null=True)),
                ("in_traffic", models.PositiveBigIntegerField(default=0)),
                ("out_traffic", models.PositiveBigIntegerField(default=0)),
                ("public", models.BooleanField(default=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("service", models.CharField(max_length=100)),
                (
                    "node_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                ("time", models.DateTimeField()),
                ("duration", models.PositiveBigIntegerField(default=None, null=True)),
                (
                    "context_type",
                    models.CharField(default=None, max_length=40, null=True),
                ),
                (
                    "context_id",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                ("host", models.URLField(default=None, null=True)),
                ("remote_log", models.TextField(default=None, null=True)),
                ("asynchronous", models.BooleanField(default=None, null=True)),
                (
                    "timeout",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=10, null=True
                    ),
                ),
                ("timeout_error", models.BooleanField(default=False)),
                ("server_error", models.BooleanField(default=False)),
                ("client_error", models.BooleanField(default=False)),
                ("ssl_error", models.BooleanField(default=False)),
                ("dns_error", models.BooleanField(default=False)),
                (
                    "alert",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_logs",
                        to="ops.alertlog",
                    ),
                ),
                (
                    "worker",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="request_logs",
                        to="ops.worker",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_request_log",
            },
        ),
        migrations.CreateModel(
            name="QueryLog",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("time", models.DateTimeField()),
                ("query", models.TextField()),
                ("duration", models.PositiveBigIntegerField(default=None, null=True)),
                ("message", models.TextField(default="")),
                ("operation", models.CharField(default=None, max_length=32, null=True)),
                (
                    "tables",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "context_type",
                    models.CharField(default=None, max_length=40, null=True),
                ),
                (
                    "context_id",
                    models.CharField(default=None, max_length=200, null=True),
                ),
                (
                    "alert",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="query_logs",
                        to="ops.alertlog",
                    ),
                ),
                (
                    "database",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="query_logs",
                        to="ops.resource",
                    ),
                ),
                (
                    "worker",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="query_logs",
                        to="ops.worker",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_query_log",
            },
        ),
        migrations.CreateModel(
            name="InstanceMonitor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cpu_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "memory_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                ("used_memory", models.PositiveBigIntegerField(default=0)),
                (
                    "disk_percent",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=6),
                ),
                (
                    "file_descriptors",
                    models.PositiveIntegerField(default=None, null=True),
                ),
                ("open_files", models.PositiveBigIntegerField(default=None, null=True)),
                ("active_net_connections", models.PositiveIntegerField(default=0)),
                ("total_net_connections", models.PositiveIntegerField(default=0)),
                (
                    "net_connections_info",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("in_traffic", models.PositiveBigIntegerField(default=0, null=True)),
                ("out_traffic", models.PositiveBigIntegerField(default=0, null=True)),
                ("outbound_requests", models.PositiveIntegerField(default=0)),
                (
                    "outbound_rps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("outbound_timeouts", models.PositiveBigIntegerField(default=0)),
                ("outbound_errors", models.PositiveBigIntegerField(default=0)),
                (
                    "outbound_avg_time",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                ("queries_num", models.PositiveBigIntegerField(default=0)),
                (
                    "query_avg_time",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "qps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("requests", models.PositiveBigIntegerField(default=0)),
                (
                    "rps",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("errors", models.PositiveBigIntegerField(default=0)),
                (
                    "avg_time",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("layer", models.PositiveSmallIntegerField(default=0)),
                ("interval", models.PositiveIntegerField(default=None, null=True)),
                ("threads", models.PositiveIntegerField(default=0)),
                ("current_workers", models.PositiveIntegerField(default=0)),
                (
                    "avg_worker_lifetime",
                    models.PositiveBigIntegerField(default=None, null=True),
                ),
                ("new_spawned_workers", models.PositiveBigIntegerField(default=0)),
                (
                    "avg_workers",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=10, null=True
                    ),
                ),
                (
                    "metrics",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "instance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="instance_metrics",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_instance_monitor",
                "ordering": ("time",),
            },
        ),
        migrations.CreateModel(
            name="DatabaseMonitor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("layer", models.PositiveSmallIntegerField(default=0)),
                ("interval", models.PositiveIntegerField(default=None, null=True)),
                ("used_space", models.PositiveBigIntegerField(default=0)),
                ("server_used_space", models.PositiveBigIntegerField(default=0)),
                ("active_connections", models.PositiveBigIntegerField(default=0)),
                ("current_connections", models.PositiveBigIntegerField(default=0)),
                ("server_connections", models.PositiveBigIntegerField(default=0)),
                ("new_transactions", models.PositiveBigIntegerField(default=0)),
                (
                    "metrics",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("queries_num", models.PositiveBigIntegerField(default=0)),
                (
                    "qps",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=10, null=True
                    ),
                ),
                (
                    "query_avg_time",
                    models.DecimalField(decimal_places=2, default=0, max_digits=10),
                ),
                (
                    "operations",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "database",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="database_metrics",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_database_monitor",
                "ordering": ("time",),
            },
        ),
        migrations.CreateModel(
            name="DatabaseConnection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("status", models.CharField(max_length=40)),
                ("active", models.BooleanField(default=False)),
                ("client_addr", models.GenericIPAddressField()),
                ("client_port", models.PositiveIntegerField()),
                ("pid", models.PositiveIntegerField(default=None, null=True)),
                ("query", models.TextField(default="")),
                ("operation", models.CharField(default=None, max_length=32, null=True)),
                (
                    "tables",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("backend_start", models.DateTimeField(default=None, null=True)),
                ("transaction_start", models.DateTimeField(default=None, null=True)),
                ("wait_event", models.TextField(default=None, null=True)),
                ("query_start", models.DateTimeField(default=None, null=True)),
                ("state_change", models.DateTimeField(default=None, null=True)),
                (
                    "data",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "database",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="database_connections",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_database_connection",
            },
        ),
        migrations.CreateModel(
            name="CacheMonitor",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "time",
                    models.DateTimeField(
                        default=utilmeta.utils.functional.time.time_now
                    ),
                ),
                ("layer", models.PositiveSmallIntegerField(default=0)),
                ("interval", models.PositiveIntegerField(default=None, null=True)),
                (
                    "cpu_percent",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=6, null=True
                    ),
                ),
                (
                    "memory_percent",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=6, null=True
                    ),
                ),
                ("used_memory", models.PositiveBigIntegerField(default=0)),
                (
                    "file_descriptors",
                    models.PositiveIntegerField(default=None, null=True),
                ),
                ("open_files", models.PositiveBigIntegerField(default=None, null=True)),
                ("current_connections", models.PositiveBigIntegerField(default=0)),
                ("total_connections", models.PositiveBigIntegerField(default=0)),
                (
                    "qps",
                    models.DecimalField(
                        decimal_places=2, default=None, max_digits=10, null=True
                    ),
                ),
                (
                    "metrics",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                (
                    "cache",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="cache_metrics",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_cache_monitor",
                "ordering": ("time",),
            },
        ),
        migrations.CreateModel(
            name="AlertType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service", models.CharField(max_length=100)),
                (
                    "node_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                ("category", models.CharField(max_length=40)),
                ("level", models.CharField(max_length=40)),
                (
                    "settings_id",
                    models.CharField(default=None, max_length=40, null=True),
                ),
                ("threshold", models.FloatField(default=None, null=True)),
                ("subcategory", models.CharField(max_length=200)),
                ("name", models.CharField(max_length=100)),
                ("target", models.CharField(max_length=100)),
                ("ident", models.CharField(max_length=200)),
                (
                    "compress_window",
                    models.PositiveBigIntegerField(default=None, null=True),
                ),
                ("min_times", models.PositiveIntegerField(default=1)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                (
                    "resource",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="alert_types",
                        to="ops.resource",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_alert_type",
                "unique_together": {("service", "ident")},
            },
        ),
        migrations.AddField(
            model_name="alertlog",
            name="instance",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="instance_alert_logs",
                to="ops.resource",
            ),
        ),
        migrations.AddField(
            model_name="alertlog",
            name="server",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="server_alert_logs",
                to="ops.resource",
            ),
        ),
        migrations.AddField(
            model_name="alertlog",
            name="type",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="alert_logs",
                to="ops.alerttype",
            ),
        ),
        migrations.AddField(
            model_name="alertlog",
            name="version",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="alert_logs",
                to="ops.versionlog",
            ),
        ),
        migrations.CreateModel(
            name="AccessToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("token_id", models.CharField(max_length=100, unique=True)),
                ("issued_at", models.DateTimeField(default=None, null=True)),
                ("subject", models.CharField(default=None, max_length=200, null=True)),
                ("expiry_time", models.DateTimeField(default=None, null=True)),
                ("last_activity", models.DateTimeField(default=None, null=True)),
                ("used_times", models.PositiveIntegerField(default=0)),
                ("ip", models.GenericIPAddressField(default=None, null=True)),
                (
                    "scope",
                    models.JSONField(
                        default=list, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("revoked", models.BooleanField(default=False)),
                (
                    "issuer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="access_tokens",
                        to="ops.supervisor",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_access_token",
            },
        ),
    ]
