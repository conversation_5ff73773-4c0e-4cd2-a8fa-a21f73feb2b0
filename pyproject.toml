[build-system]
requires = ["hatchling >= 1.13.0"]
build-backend = "hatchling.build"

[project]
name = "UtilMeta"
description = "UtilMeta - progressive meta framework for API development in Python"
readme = "README.md"
requires-python = ">=3.8"
license = {file = "LICENSE"}
authors = [
    { name = "<PERSON><PERSON> (voidZXL)", email = "<EMAIL>" },
]
keywords = ["API", "backend", "orm", "RESTful", "meta", "progressive", "declarative", "web", "utype", "devops"]
classifiers = [
    "Intended Audience :: Information Technology",
    "Intended Audience :: System Administrators",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python",
    "Topic :: Internet",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development",
    "Typing :: Typed",
    "Development Status :: 4 - Beta",
    "Environment :: Web Environment",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Internet :: WWW/HTTP",
]
dependencies = [
    "utype>=0.6.6",
    "django>=4.1.0",
    "databases>=0.7.0",
    "jwcrypto>=1.5.4",
    "psutil>=5.8.0",
    "user_agents>=2.2.0",
    "aiosqlite>=0.20.0"
]

dynamic = ["version"]

[project.urls]
Homepage = "https://utilmeta.com/py"
Documentation = "https://docs.utilmeta.com/py/en"
Repository = "https://github.com/utilmeta/utilmeta-py"

[project.optional-dependencies]
all = [
    "starlette >= 0.27.0",
    "sanic >= 22.0.0",
    "flask >= 2.3.0",
    "tornado >= 6.2",
]

[tool.hatch.version]
path = "utilmeta/__init__.py"

[tool.hatch.build.targets.sdist]
exclude = [
    "/.github",
    "/docs",
    "/tests"
]

[tool.hatch.build.targets.wheel]
packages = ["utilmeta"]

[project.scripts]
meta = "utilmeta.bin.meta:main"
utilmeta = "utilmeta.bin.meta:main"