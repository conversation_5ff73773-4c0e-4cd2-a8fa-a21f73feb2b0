name: UtilMeta Common CI
# run before every pull request and every push
on:
  pull_request:
    paths:
      - '.github/workflows/**'
      - 'tests/**'
      - 'utilmeta/**'
  push:
    paths:
      - '.github/workflows/**'
      - 'tests/**'
      - 'utilmeta/**'

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
#        os: [macos-latest, windows-latest, ubuntu-latest]
#        python-version: ["3.8", "3.11", "3.13"]
#        django-version: ["4.1", "5.0", "5.1"]
        include:
          - os: ubuntu-latest
            python-version: "3.13"
            django-version: "5.1"
            name: "3.13 on Linux"
#          - os: windows-latest
#            python-version: "3.13"
#            django-version: "5.1"
#            name: "3.13 on Windows"
#          - os: macos-latest
#            python-version: "3.13"
#            django-version: "5.1"
#            name: "3.13 on Mac"
          - os: ubuntu-latest
            python-version: "3.11"
            django-version: "5.0"
            name: "3.11 (django 5.0)"
          - os: ubuntu-latest
            python-version: "3.10"
            django-version: "4.2"
            name: "3.10 (django 4.2)"
          - os: ubuntu-latest
            python-version: "3.9"
            django-version: "4.1"
            name: "3.9 (django 4.1)"
          - os: ubuntu-latest
            python-version: "3.8"
            django-version: "4.0"
            name: "3.8 (django 4.0)"
          - os: ubuntu-latest
            python-version: "3.8"
            django-version: "3.0"
            name: "Django compat earliest (3.0)"

    name: ${{ matrix.name }}
    runs-on: ${{ matrix.os }}

    services:
      postgres:
        image: postgres:17
        ports:
          - 5432:5432
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test-tmp-password
          POSTGRES_DB: utilmeta_test_db
        options: >-
          --health-cmd="pg_isready -U test_user"
          --health-interval=5s
          --health-timeout=5s
          --health-retries=5

    env:
      DEFAULT_DB_DATABASE: utilmeta_test_db
      OPERATIONS_DB_DATABASE: utilmeta_test_ops_db
      MYSQL_DB_USER: root
      MYSQL_DB_PASSWORD: root

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 pytest pytest-cov pytest-asyncio
          pip install jwcrypto psutil jwt
          pip install utype
          pip install databases[aiosqlite] redis>=4.2.0rc1 psycopg2 mysqlclient
          pip install django==${{ matrix.django-version }}
          pip install flask apiflask fastapi tornado aiohttp uvicorn httpx requests python-multipart
          pip install sanic==24.6.0
          pip install sanic-ext==23.12.0
      # frozen sanic[ext] version to adapt py 3.8
      - name: Install conditional dependencies
        if: ${{ matrix.django-version >= '4.0'}}
        run: |
          pip install django-ninja djangorestframework drf-spectacular
      - name: Run lint
        run: |
          flake8 utilmeta --count --select=E9,F63,F7,F82 --show-source --statistics

      - name: Setup MySQL
        run: |
          sudo /etc/init.d/mysql start
          mysql -e "CREATE DATABASE ${{ env.DEFAULT_DB_DATABASE }};" -u${{ env.MYSQL_DB_USER }} -p${{ env.MYSQL_DB_PASSWORD }}
          mysql -e "CREATE DATABASE ${{ env.OPERATIONS_DB_DATABASE }};" -u${{ env.MYSQL_DB_USER }} -p${{ env.MYSQL_DB_PASSWORD }}
          mysql -e "CREATE USER 'test_user'@'%' IDENTIFIED BY 'test-tmp-password';" -u${{ env.MYSQL_DB_USER }} -p${{ env.MYSQL_DB_PASSWORD }}
          mysql -e "GRANT ALL PRIVILEGES ON ${{ env.DEFAULT_DB_DATABASE }}.* TO 'test_user'@'%';" -u${{ env.MYSQL_DB_USER }} -p${{ env.MYSQL_DB_PASSWORD }}
          mysql -e "GRANT ALL PRIVILEGES ON ${{ env.OPERATIONS_DB_DATABASE }}.* TO 'test_user'@'%';" -u${{ env.MYSQL_DB_USER }} -p${{ env.MYSQL_DB_PASSWORD }}
          mysql -e "FLUSH PRIVILEGES;" -u${{ env.MYSQL_DB_USER }} -p${{ env.MYSQL_DB_PASSWORD }}

      - name: Wait for PostgreSQL
        run: |
          for i in {1..30}; do
            if pg_isready -h localhost -p 5432 -U test_user; then
              echo "PostgreSQL is ready!"
              break
            fi
            echo "Waiting for PostgreSQL to be ready..."
            sleep 5
          done
          PGPASSWORD=test-tmp-password psql -h localhost -p 5432 -U test_user -d utilmeta_test_db -c "CREATE DATABASE utilmeta_test_ops_db;"

      - name: Run tests
        run: |
          pytest tests --cov=./utilmeta
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
