# Generated by UtilMeta 2.6.0 on 2024-11-13 05:54
# generator spec: openapi 3.1.0
# generator class: utilmeta.core.cli.specs.openapi.OpenAPIClientGenerator
from utilmeta.core import api, cli, response, request
import utype


class schemas:
    class UserSchema(utype.Schema):
        username: str
        articles_num: int

    class ArticleSchema(utype.Schema):
        id: int
        author: "schemas.UserSchema"
        content: str


class responses:
    class article_get_response(response.Response):
        content_type = "application/json"
        result: schemas.ArticleSchema


class APIClient(cli.Client):
    @api.get("/article", tags=["article"])
    async def article_get(
        self, id: int = request.QueryParam(required=True)
    ) -> responses.article_get_response[200]: pass


import httpx
client = APIClient(base_url="http://127.0.0.1:8080", backend=httpx)
# >>> resp = await client.article_get(id=1)
# >>> resp
# article_get_response [200 OK] "GET /article?id=1"
# >>> resp.result
# ArticleSchema(id=1, author=UserSchema(username='alice', articles_num=1), content='Hello World')
