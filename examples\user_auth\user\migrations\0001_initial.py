# Generated by Django 4.2.1 on 2024-01-24 16:46

from django.db import migrations, models
import django.db.models.deletion
import utilmeta.core.orm.backends.django.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(max_length=20, unique=True)),
                (
                    "password",
                    utilmeta.core.orm.backends.django.models.PasswordField(
                        max_length=100, min_length=1, regex=None, salt_length=32
                    ),
                ),
                ("signup_time", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="Session",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_key", models.CharField(max_length=60, unique=True)),
                ("encoded_data", models.TextField(null=True)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("last_activity", models.DateTimeField(default=None, null=True)),
                ("expiry_time", models.DateTimeField(default=None, null=True)),
                ("deleted_time", models.DateTimeField(default=None, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="user.user",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
