# Generated by Django 4.2.17 on 2025-03-19 03:13

from django.db import migrations, models


CREATE_SQL = """
CREATE VIEW article_stats AS
    SELECT
        a.basecontent_ptr_id AS article_id,
        COUNT(DISTINCT l.user_id) AS liked_bys_num,
        COUNT(DISTINCT c.basecontent_ptr_id) AS comments_num
    FROM
        article a
    INNER JOIN
        content ct ON a.basecontent_ptr_id = ct.id
    LEFT JOIN
        liked l ON ct.id = l.basecontent_id
    LEFT JOIN
        comment c ON ct.id = c.on_content_id
    GROUP BY
        a.basecontent_ptr_id;
"""

DROP_SQL = "DROP VIEW article_stats;"


class Migration(migrations.Migration):

    dependencies = [
        ("app", "0002_article_tags"),
    ]

    operations = [
        migrations.CreateModel(
            name="Statistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField()),
                ("articles_num", models.PositiveIntegerField(default=0)),
                ("comments_num", models.PositiveIntegerField(default=0)),
                ("users_num", models.PositiveIntegerField(default=0)),
            ],
            options={
                "db_table": "stats",
                "managed": False,
            },
        ),
        migrations.RunSQL(
            sql=CREATE_SQL,
            reverse_sql=DROP_SQL,
        ),
    ]
