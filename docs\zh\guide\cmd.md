# 命令行工具

UtilMeta 框架在安装后会提供一个命令行工具，名称是 `utilmeta`，简称 `meta`，你可以在安装框架后的地址栏执行

```shell
meta
```

可以看到


## 创建项目


## 项目初始化与 `meta.ini`

如果你并不是要创建一个新的项目，而是在现有的 Python 项目中引入 UtilMeta 框架的接口或运维能力

```shell
meta init
```


## 运行与部署


接下来的版本中 UtilMeta 将会提供自动化部署命令，能够自动生成并更新 uwsgi, gunicorn, nginx 等配置文件，以及注册与管理 systemd 服务

## 文档与客户端代码生成

### 生成 OpenAPI 文档


### 为 UtilMeta 服务生成客户端代码


## 扩展命令

### `Operations` 运维管理命令


### django 命令


## 自定义命令

### `Arg` 参数
