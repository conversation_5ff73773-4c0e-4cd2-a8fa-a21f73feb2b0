# Generated by Django 4.2.1 on 2024-03-06 03:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("ops", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="servicelog",
            name="access_token",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="logs",
                to="ops.accesstoken",
            ),
        ),
        migrations.AddField(
            model_name="servicelog",
            name="endpoint_ident",
            field=models.CharField(default=None, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="servicelog",
            name="endpoint_ref",
            field=models.CharField(default=None, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="servicelog",
            name="supervisor",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="logs",
                to="ops.supervisor",
            ),
        ),
    ]
