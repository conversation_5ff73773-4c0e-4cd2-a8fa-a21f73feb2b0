# Generated by Django 4.2.1 on 2024-03-14 08:26

from django.db import migrations, models
import django.db.models.deletion
import utype.utils.encode


class Migration(migrations.Migration):

    dependencies = [
        ("ops", "0002_servicelog_access_token_servicelog_endpoint_ident_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AggregationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service", models.CharField(max_length=100)),
                (
                    "node_id",
                    models.CharField(
                        db_index=True, default=None, max_length=100, null=True
                    ),
                ),
                (
                    "data",
                    models.JSONField(
                        default=dict, encoder=utype.utils.encode.JSONEncoder
                    ),
                ),
                ("layer", models.PositiveSmallIntegerField(default=0)),
                ("from_time", models.DateTimeField()),
                ("to_time", models.DateTimeField()),
                ("date", models.DateField(default=None, null=True)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("reported_time", models.DateTimeField(default=None, null=True)),
                ("error", models.TextField(default=None, null=True)),
                ("remote_id", models.CharField(default=None, max_length=40, null=True)),
                (
                    "supervisor",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="aggregation_logs",
                        to="ops.supervisor",
                    ),
                ),
            ],
            options={
                "db_table": "utilmeta_aggregation_log",
            },
        ),
    ]
