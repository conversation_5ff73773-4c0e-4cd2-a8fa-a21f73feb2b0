# Generated by Django 4.2.1 on 2024-04-12 12:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ops", "0003_aggregationlog"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="accesstoken",
            name="subject",
            field=models.Char<PERSON><PERSON>(default=None, max_length=500, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="accesstoken",
            name="token_id",
            field=models.Char<PERSON>ield(max_length=200, unique=True),
        ),
        migrations.AlterField(
            model_name="alerttype",
            name="ident",
            field=models.Char<PERSON>ield(max_length=500),
        ),
        migrations.AlterField(
            model_name="alerttype",
            name="target",
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name="resource",
            name="remote_id",
            field=models.Char<PERSON>ield(default=None, max_length=100, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="resource",
            name="route",
            field=models.<PERSON><PERSON><PERSON><PERSON>(max_length=500),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="supervisor",
            name="init_key",
            field=models.Text<PERSON>ield(default=None, null=True),
        ),
        migrations.AlterField(
            model_name="supervisor",
            name="resources_etag",
            field=models.TextField(default=None, null=True),
        ),
    ]
