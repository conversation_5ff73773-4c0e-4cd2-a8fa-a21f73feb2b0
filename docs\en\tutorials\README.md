# Case Tutorials

We have designed several introductory cases from easy to complex, covering most of the usage of the framework from shallow to deep. You can read and learn in the following order.

[💻 BMI Calculation API](bmi-calc)

!!! note ""
	Write a simple BMI calculation API, return the BMI value based on the input height and weight

[🙋‍♂️ User login & RESTful API ](user-auth)

!!! note ""
	Build an API that offers user signup, login, query and update info

[✍️  Realworld Blog Project ](realworld-blog)

!!! note ""
	Build APIs for a classic blog project with features including
	
	* User registration, login, access, update information, follow, unfollow
	* Article creation, modification, likes, recommendations, article comment creation and deletion

WebSocket Chatroom (coming soon)
