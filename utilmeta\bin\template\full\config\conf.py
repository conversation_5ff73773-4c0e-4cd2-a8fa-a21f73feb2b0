from utilmeta import UtilMeta, conf
from config.env import env


def configure(service: UtilMeta):
    from utilmeta.core.server.backends.django import DjangoSettings
    from utilmeta.core.orm import DatabaseConnections, Database

    service.use(DjangoSettings(apps_package="domain", secret_key=env.DJANGO_SECRET_KEY))
    service.use(
        DatabaseConnections(
            {
                "default": Database(
                    name="db",
                    engine="sqlite3",
                )
            }
        )
    )
    {operations}  # noqa
