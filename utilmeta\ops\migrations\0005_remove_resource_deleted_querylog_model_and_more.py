# Generated by Django 4.2.13 on 2024-10-27 11:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("ops", "0004_alter_accesstoken_subject_alter_accesstoken_token_id_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="resource",
            name="deleted",
        ),
        migrations.AddField(
            model_name="querylog",
            name="model",
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="model_query_logs",
                to="ops.resource",
            ),
        ),
        migrations.AddField(
            model_name="resource",
            name="deleted_time",
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AddField(
            model_name="resource",
            name="ref",
            field=models.CharField(default=None, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="resource",
            name="updated_time",
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AlterField(
            model_name="querylog",
            name="database",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="database_query_logs",
                to="ops.resource",
            ),
        ),
    ]
