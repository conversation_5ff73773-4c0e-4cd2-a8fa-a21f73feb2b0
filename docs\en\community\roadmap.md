# RoadMap

## Long term planing

### WebSocket / SSE

* Support writing WebSocket / SSE / Server Push APIs in declarative way
* Support document generation and testing for WebSocket / SSE APIs

### GraphQL

* Support `orm` module building the GraphQL APIs
* Support GraphQL API document generation and testing

### ORM

* **SQLAlchemy**
* **Peewee**

### Runtime Backend

* **Bottle.py**
* **Pyramid**

### Operations system

* Custom realtime alerting rules
* Support mainstream metrics data source (like ElasticSearch and Prometheus)
* More abilities in testing, include APIs test and stress test


## Version roadmap

### v2.8

**New Features**

* Support auto generating uWSGI and Gunicorn config files
* Support custom realtime alerting rules

